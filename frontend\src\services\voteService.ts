import { mockApiService } from "./mockApiService";

// Toggle this to switch between mock and real API
const USE_MOCK_API = false;

export const voteService = {
  async voteOnQuestion(
    questionId: string,
    isUpvote: boolean
  ): Promise<{ voteCount: number; userVote: "up" | "down" | null }> {
    if (USE_MOCK_API) {
      return mockApiService.voteOnQuestion(questionId, isUpvote);
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    const voteData = { questionId: parseInt(questionId), isUpvote };
    return apiClient.post<{
      voteCount: number;
      userVote: "up" | "down" | null;
    }>("/votes/question", voteData);
  },

  async voteOnAnswer(
    answerId: string,
    isUpvote: boolean
  ): Promise<{ voteCount: number; userVote: "up" | "down" | null }> {
    if (USE_MOCK_API) {
      return mockApiService.voteOnAnswer(answerId, isUpvote);
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    const voteData = { answerId: parseInt(answerId), isUpvote };
    return apiClient.post<{
      voteCount: number;
      userVote: "up" | "down" | null;
    }>("/votes/answer", voteData);
  },
};
