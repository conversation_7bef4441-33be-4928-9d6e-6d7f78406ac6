import { mockApiService } from "./mockApiService";

// Toggle this to switch between mock and real API
const USE_MOCK_API = true;

export const voteService = {
  async voteOnQuestion(
    questionId: string,
    isUpvote: boolean
  ): Promise<{ voteCount: number; userVote: "up" | "down" | null }> {
    if (USE_MOCK_API) {
      return mockApiService.voteOnQuestion(questionId, isUpvote);
    }
    // Real API implementation would go here
    throw new Error("Real API not implemented");
  },

  async voteOnAnswer(
    answerId: string,
    isUpvote: boolean
  ): Promise<{ voteCount: number; userVote: "up" | "down" | null }> {
    if (USE_MOCK_API) {
      return mockApiService.voteOnAnswer(answerId, isUpvote);
    }
    // Real API implementation would go here
    throw new Error("Real API not implemented");
  },
};
