import { mockApiService } from "./mockApiService";

// Toggle this to switch between mock and real API
const USE_MOCK_API = false;

export const voteService = {
  async voteOnQuestion(
    questionId: number,
    isUpvote: boolean
  ): Promise<{ voteCount: number; userVote: "up" | "down" | null }> {
    if (USE_MOCK_API) {
      return mockApiService.voteOnQuestion(questionId, isUpvote);
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    const voteData = { questionId, isUpvote };
    return apiClient.post<{
      voteCount: number;
      userVote: "up" | "down" | null;
    }>("/votes/question", voteData);
  },

  async voteOnAnswer(
    answerId: number,
    isUpvote: boolean
  ): Promise<{ voteCount: number; userVote: "up" | "down" | null }> {
    if (USE_MOCK_API) {
      return mockApiService.voteOnAnswer(answerId, isUpvote);
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    const voteData = { answerId, isUpvote };
    return apiClient.post<{
      voteCount: number;
      userVote: "up" | "down" | null;
    }>("/votes/answer", voteData);
  },
};
