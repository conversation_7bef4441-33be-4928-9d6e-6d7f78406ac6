import { Tag } from "../types";
import { mockApiService } from "./mockApiService";

// Toggle this to switch between mock and real API
const USE_MOCK_API = true;

export const tagService = {
  async getAllTags(): Promise<Tag[]> {
    if (USE_MOCK_API) {
      return mockApiService.getAllTags();
    }
    // Real API implementation would go here
    throw new Error("Real API not implemented");
  },

  async searchTags(query: string): Promise<Tag[]> {
    if (USE_MOCK_API) {
      return mockApiService.searchTags(query);
    }
    // Real API implementation would go here
    throw new Error("Real API not implemented");
  },
};
