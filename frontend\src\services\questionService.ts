import {
  Question,
  CreateQuestionRequest,
  UpdateQuestionRequest,
  QuestionFilters,
  PaginatedResponse,
} from "../types";
import { mockApiService } from "./mockApiService";

// Toggle this to switch between mock and real API
const USE_MOCK_API = false;

export const questionService = {
  async getQuestions(
    filters?: QuestionFilters
  ): Promise<PaginatedResponse<Question>> {
    if (USE_MOCK_API) {
      return mockApiService.getQuestions(filters);
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    return apiClient.get<PaginatedResponse<Question>>("/questions", filters);
  },

  async getQuestionById(id: string): Promise<Question> {
    if (USE_MOCK_API) {
      return mockApiService.getQuestionById(id);
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    return apiClient.get<Question>(`/questions/${id}`);
  },

  async createQuestion(questionData: CreateQuestionRequest): Promise<Question> {
    if (USE_MOCK_API) {
      return mockApiService.createQuestion(questionData);
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    return apiClient.post<Question>("/questions", questionData);
  },

  async updateQuestion(
    id: string,
    questionData: UpdateQuestionRequest
  ): Promise<Question> {
    if (USE_MOCK_API) {
      throw new Error("Mock API does not support question updates");
    }
    // Real API implementation would go here
    throw new Error("Real API not implemented");
  },

  async deleteQuestion(id: string): Promise<void> {
    if (USE_MOCK_API) {
      throw new Error("Mock API does not support question deletion");
    }
    // Real API implementation would go here
    throw new Error("Real API not implemented");
  },

  async searchQuestions(
    query: string,
    filters?: Omit<QuestionFilters, "search">
  ): Promise<PaginatedResponse<Question>> {
    if (USE_MOCK_API) {
      return mockApiService.getQuestions({
        search: query,
        ...filters,
      });
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    return apiClient.get<PaginatedResponse<Question>>("/questions", {
      search: query,
      ...filters,
    });
  },
};
