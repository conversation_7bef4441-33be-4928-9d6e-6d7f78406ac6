import { Answer, CreateAnswerRequest, UpdateAnswerRequest } from "../types";
import { mockApiService } from "./mockApiService";

// Toggle this to switch between mock and real API
const USE_MOCK_API = true;

export const answerService = {
  async createAnswer(answerData: CreateAnswerRequest): Promise<Answer> {
    if (USE_MOCK_API) {
      return mockApiService.createAnswer(answerData);
    }
    // Real API implementation would go here
    throw new Error("Real API not implemented");
  },

  async updateAnswer(
    id: string,
    answerData: UpdateAnswerRequest
  ): Promise<Answer> {
    if (USE_MOCK_API) {
      return mockApiService.updateAnswer(id, answerData);
    }
    // Real API implementation would go here
    throw new Error("Real API not implemented");
  },

  async deleteAnswer(id: string): Promise<void> {
    if (USE_MOCK_API) {
      return mockApiService.deleteAnswer(id);
    }
    // Real API implementation would go here
    throw new Error("Real API not implemented");
  },
};
