import { Answer, CreateAnswerRequest, UpdateAnswerRequest } from "../types";
import { mockApiService } from "./mockApiService";

// Toggle this to switch between mock and real API
const USE_MOCK_API = false;

export const answerService = {
  async createAnswer(answerData: CreateAnswerRequest): Promise<Answer> {
    if (USE_MOCK_API) {
      return mockApiService.createAnswer(answerData);
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    return apiClient.post<Answer>(
      `/questions/${answerData.questionId}/answers`,
      answerData
    );
  },

  async updateAnswer(
    id: string,
    answerData: UpdateAnswerRequest
  ): Promise<Answer> {
    if (USE_MOCK_API) {
      return mockApiService.updateAnswer(id, answerData);
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    return apiClient.put<Answer>(`/answers/${id}`, answerData);
  },

  async deleteAnswer(id: string): Promise<void> {
    if (USE_MOCK_API) {
      return mockApiService.deleteAnswer(id);
    }
    // Real API implementation
    const { apiClient } = await import("./api");
    return apiClient.delete<void>(`/answers/${id}`);
  },
};
